/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

export type { AddHouseHoldMain } from './models/add-house-hold-main';
export type { ApproveRecord } from './models/approve-record';
export type { ApproveWaitingArgs } from './models/approve-waiting-args';
export type { ApproveWaitingByIdArgs } from './models/approve-waiting-by-id-args';
export type { ApproveWaitingByIdRes } from './models/approve-waiting-by-id-res';
export type { ApproveWaitingByIdResResponseBase } from './models/approve-waiting-by-id-res-response-base';
export type { ApproveWaitingRes } from './models/approve-waiting-res';
export type { ApproveWaitingResListResponseBase } from './models/approve-waiting-res-list-response-base';
export type { BooleanResponseBase } from './models/boolean-response-base';
export type { BuildCaseFileRes } from './models/build-case-file-res';
export type { BuildCaseFileResListResponseBase } from './models/build-case-file-res-list-response-base';
export type { BuildCaseFileResResponseBase } from './models/build-case-file-res-response-base';
export type { BuildCaseGetFileArgs } from './models/build-case-get-file-args';
export type { BuildCaseGetFileRespone } from './models/build-case-get-file-respone';
export type { BuildCaseGetFileResponeListResponseBase } from './models/build-case-get-file-respone-list-response-base';
export type { BuildCaseGetListReponse } from './models/build-case-get-list-reponse';
export type { BuildCaseGetListReponseListResponseBase } from './models/build-case-get-list-reponse-list-response-base';
export type { BuildCaseGetListReponseResponseBase } from './models/build-case-get-list-reponse-response-base';
export type { BuildingSample } from './models/building-sample';
export type { ByteArrayResponseBase } from './models/byte-array-response-base';
export type { CancelChangePreOrder } from './models/cancel-change-pre-order';
export type { ChangePasswordRequest } from './models/change-password-request';
export type { CheckOtpRequest } from './models/check-otp-request';
export type { CHouse } from './models/c-house';
export type { CreateAppointmentArgs } from './models/create-appointment-args';
export type { CreateFinalDocArgs } from './models/create-final-doc-args';
export type { CreateListFormItem } from './models/create-list-form-item';
export type { DeleteBuildCaseArgs } from './models/delete-build-case-args';
export type { DeleteRegularPic } from './models/delete-regular-pic';
export type { DeleteRequirementRequest } from './models/delete-requirement-request';
export type { DeleteSpaceRequest } from './models/delete-space-request';
export type { EditAppointmentArgs } from './models/edit-appointment-args';
export type { EditHouseArgs } from './models/edit-house-args';
export type { EditHouseInfo } from './models/edit-house-info';
export type { EditHouseRegularPicture } from './models/edit-house-regular-picture';
export type { EditHouseRegularPictureArgs } from './models/edit-house-regular-picture-args';
export type { EditListHouseArgs } from './models/edit-list-house-args';
export type { EnumArgs } from './models/enum-args';
export { EnumQuotationItemType } from './models/enum-quotation-item-type';
export type { EnumResponse } from './models/enum-response';
export type { EnumResponseListResponseBase } from './models/enum-response-list-response-base';
export { EnumStatusCode } from './models/enum-status-code';
export type { ExportExcelMaterials } from './models/export-excel-materials';
export type { ExportExcelMaterialsResponseBase } from './models/export-excel-materials-response-base';
export type { ExportExcelResponse } from './models/export-excel-response';
export type { ExportExcelResponseResponseBase } from './models/export-excel-response-response-base';
export type { FileApprove } from './models/file-approve';
export type { FileRes } from './models/file-res';
export type { FileViewModel } from './models/file-view-model';
export type { FloorRange } from './models/floor-range';
export type { FormItems } from './models/form-items';
export type { FunctionDto } from './models/function-dto';
export type { GetAllBuildCaseArgs } from './models/get-all-build-case-args';
export type { GetAppoinmentArgs } from './models/get-appoinment-args';
export type { GetAppoinmentRes } from './models/get-appoinment-res';
export type { GetAppoinmentResListResponseBase } from './models/get-appoinment-res-list-response-base';
export type { GetAppoinmentResResponseBase } from './models/get-appoinment-res-response-base';
export type { GetBuildCaseById } from './models/get-build-case-by-id';
export type { GetBuildCaseFileById } from './models/get-build-case-file-by-id';
export type { GetBuildCaseMailListRequest } from './models/get-build-case-mail-list-request';
export type { GetBuildCaseMailListResponse } from './models/get-build-case-mail-list-response';
export type { GetBuildCaseMailListResponseListResponseBase } from './models/get-build-case-mail-list-response-list-response-base';
export type { GetBuildingSampleSelectionRes } from './models/get-building-sample-selection-res';
export type { GetBuildingSampleSelectionResResponseBase } from './models/get-building-sample-selection-res-response-base';
export type { GetChangePreOrderArgs } from './models/get-change-pre-order-args';
export type { GetChangePreOrderRespone } from './models/get-change-pre-order-respone';
export type { GetChangePreOrderResponeResponseBase } from './models/get-change-pre-order-respone-response-base';
export type { GetDisclaimerArgs } from './models/get-disclaimer-args';
export type { GetFinalDocAfter } from './models/get-final-doc-after';
export type { GetFinalDocBefore } from './models/get-final-doc-before';
export type { GetFinalDocListByHouse } from './models/get-final-doc-list-by-house';
export type { GetFinalDocRes } from './models/get-final-doc-res';
export type { GetFinalDocResListResponseBase } from './models/get-final-doc-res-list-response-base';
export type { GetHourListAppointmentReq } from './models/get-hour-list-appointment-req';
export type { GetHourListRespone } from './models/get-hour-list-respone';
export type { GetHourListResponeListResponseBase } from './models/get-hour-list-respone-list-response-base';
export type { GetHouseAndFloorByBuildCaseIdRes } from './models/get-house-and-floor-by-build-case-id-res';
export type { GetHouseAndFloorByBuildCaseIdResListResponseBase } from './models/get-house-and-floor-by-build-case-id-res-list-response-base';
export type { GetHouseByIdArgs } from './models/get-house-by-id-args';
export type { GetHouseChangeDateReq } from './models/get-house-change-date-req';
export type { GetHouseChangeDateRes } from './models/get-house-change-date-res';
export type { GetHouseChangeDateResListResponseBase } from './models/get-house-change-date-res-list-response-base';
export type { GetHouseListArgs } from './models/get-house-list-args';
export type { GetHouseListRes } from './models/get-house-list-res';
export type { GetHouseListResListResponseBase } from './models/get-house-list-res-list-response-base';
export type { GetHouseProgress } from './models/get-house-progress';
export type { GetHouseProgressResponseBase } from './models/get-house-progress-response-base';
export type { GetHouseReview } from './models/get-house-review';
export type { GetHouseReviewListResponseBase } from './models/get-house-review-list-response-base';
export type { GetListBuildCaseFileArgs } from './models/get-list-build-case-file-args';
export type { GetListBuildingArgs } from './models/get-list-building-args';
export type { GetListByHouseIdRequest } from './models/get-list-by-house-id-request';
export type { GetListFinalDocArgs } from './models/get-list-final-doc-args';
export type { GetListFinalDocRes } from './models/get-list-final-doc-res';
export type { GetListFinalDocResListResponseBase } from './models/get-list-final-doc-res-list-response-base';
export type { GetListFormItemReq } from './models/get-list-form-item-req';
export type { GetListFormItemRes } from './models/get-list-form-item-res';
export type { GetListFormItemResResponseBase } from './models/get-list-form-item-res-response-base';
export type { GetListHouseHoldArgs } from './models/get-list-house-hold-args';
export type { GetListHouseHoldRes } from './models/get-list-house-hold-res';
export type { GetListHouseHoldResListResponseBase } from './models/get-list-house-hold-res-list-response-base';
export type { GetListHouseRegularPicArgs } from './models/get-list-house-regular-pic-args';
export type { GetListHouseRegularPicRes } from './models/get-list-house-regular-pic-res';
export type { GetListHouseRegularPicResListResponseBase } from './models/get-list-house-regular-pic-res-list-response-base';
export type { GetListQuotationRequest } from './models/get-list-quotation-request';
export type { GetListRegularChangeItemRes } from './models/get-list-regular-change-item-res';
export type { GetListRegularChangeItemResListResponseBase } from './models/get-list-regular-change-item-res-list-response-base';
export type { GetListRequirementRequest } from './models/get-list-requirement-request';
export type { GetListSpecialChangeRequest } from './models/get-list-special-change-request';
export type { GetMaterialListRequest } from './models/get-material-list-request';
export type { GetMaterialListResponse } from './models/get-material-list-response';
export type { GetMaterialListResponseListResponseBase } from './models/get-material-list-response-list-response-base';
export type { GetMenuArgs } from './models/get-menu-args';
export type { GetMenuResponse } from './models/get-menu-response';
export type { GetMenuResponseResponseBase } from './models/get-menu-response-response-base';
export type { GetMilestoneRes } from './models/get-milestone-res';
export type { GetMilestoneResResponseBase } from './models/get-milestone-res-response-base';
export type { GetPayStatus } from './models/get-pay-status';
export type { GetPayStatusResponseBase } from './models/get-pay-status-response-base';
export type { GetPictureListRequest } from './models/get-picture-list-request';
export type { GetPictureListResponse } from './models/get-picture-list-response';
export type { GetPictureListResponseListResponseBase } from './models/get-picture-list-response-list-response-base';
export type { GetPreOrderSettingArgs } from './models/get-pre-order-setting-args';
export type { GetPreOrderSettingResponse } from './models/get-pre-order-setting-response';
export type { GetPreOrderSettingResponseListResponseBase } from './models/get-pre-order-setting-response-list-response-base';
export type { GetQuotation } from './models/get-quotation';
export type { GetQuotationByIdRequest } from './models/get-quotation-by-id-request';
export type { GetQuotationListResponseBase } from './models/get-quotation-list-response-base';
export type { GetQuotationResponseBase } from './models/get-quotation-response-base';
export type { GetQuotationVersions } from './models/get-quotation-versions';
export type { GetQuotationVersionsListResponseBase } from './models/get-quotation-versions-list-response-base';
export type { GetRegularChangeDetailByItemIdRes } from './models/get-regular-change-detail-by-item-id-res';
export type { GetRegularChangeDetailByItemIdResResponseBase } from './models/get-regular-change-detail-by-item-id-res-response-base';
export type { GetRegularNoticeFileByIdRes } from './models/get-regular-notice-file-by-id-res';
export type { GetRegularNoticeFileByIdResResponseBase } from './models/get-regular-notice-file-by-id-res-response-base';
export type { GetRegularNoticeFileListReq } from './models/get-regular-notice-file-list-req';
export type { GetRegularNoticeFileListRes } from './models/get-regular-notice-file-list-res';
export type { GetRegularNoticeFileListResResponseBase } from './models/get-regular-notice-file-list-res-response-base';
export type { GetRequirement } from './models/get-requirement';
export type { GetRequirementByIdRequest } from './models/get-requirement-by-id-request';
export type { GetRequirementListResponseBase } from './models/get-requirement-list-response-base';
export type { GetRequirementResponseBase } from './models/get-requirement-response-base';
export type { GetReviewByIdRes } from './models/get-review-by-id-res';
export type { GetReviewByIdResResponseBase } from './models/get-review-by-id-res-response-base';
export type { GetReviewListReq } from './models/get-review-list-req';
export type { GetReviewListRes } from './models/get-review-list-res';
export type { GetReviewListResListResponseBase } from './models/get-review-list-res-list-response-base';
export type { GetSpaceByIdRequest } from './models/get-space-by-id-request';
export type { GetSpaceListRequest } from './models/get-space-list-request';
export type { GetSpaceListResponse } from './models/get-space-list-response';
export type { GetSpaceListResponseListResponseBase } from './models/get-space-list-response-list-response-base';
export type { GetSpaceListResponseResponseBase } from './models/get-space-list-response-response-base';
export type { GetSpecialChangeFileArgs } from './models/get-special-change-file-args';
export type { GetSpecialNoticeFileByIdRes } from './models/get-special-notice-file-by-id-res';
export type { GetSpecialNoticeFileByIdResResponseBase } from './models/get-special-notice-file-by-id-res-response-base';
export type { GetSpecialNoticeFileListReq } from './models/get-special-notice-file-list-req';
export type { GetSpecialNoticeFileListRes } from './models/get-special-notice-file-list-res';
export type { GetSpecialNoticeFileListResResponseBase } from './models/get-special-notice-file-list-res-response-base';
export type { GetSumaryRegularChangeItemRes } from './models/get-sumary-regular-change-item-res';
export type { GetSumaryRegularChangeItemResListResponseBase } from './models/get-sumary-regular-change-item-res-list-response-base';
export type { GetTemplateByIdArgs } from './models/get-template-by-id-args';
export type { GetTemplateDetailByIdArgs } from './models/get-template-detail-by-id-args';
export type { GetUserBuildCaseArgs } from './models/get-user-build-case-args';
export type { HouseChangePreOrderArgs } from './models/house-change-pre-order-args';
export type { HouseDropDownItem } from './models/house-drop-down-item';
export type { HouseGetChangeDateArgs } from './models/house-get-change-date-args';
export type { HouseGetChangeDateRespone } from './models/house-get-change-date-respone';
export type { HouseGetChangeDateResponeResponseBase } from './models/house-get-change-date-respone-response-base';
export type { HouseGetHourListArgs } from './models/house-get-hour-list-args';
export type { HouseLoginRequest } from './models/house-login-request';
export type { HouseLoginResponse } from './models/house-login-response';
export type { HouseLoginResponseResponseBase } from './models/house-login-response-response-base';
export type { HouseLoginStep2Request } from './models/house-login-step-2-request';
export type { HouseRegularPic } from './models/house-regular-pic';
export type { HouseRegularPicResponseBase } from './models/house-regular-pic-response-base';
export type { HouseRequirement } from './models/house-requirement';
export type { HouseRequirementRes } from './models/house-requirement-res';
export type { HouseRequirementResListResponseBase } from './models/house-requirement-res-list-response-base';
export type { HouseRes } from './models/house-res';
export type { HouseReview } from './models/house-review';
export type { HouseSpecialNoticeFile } from './models/house-special-notice-file';
export type { LoadDefaultItemsRequest } from './models/load-default-items-request';
export type { LockFormItemReq } from './models/lock-form-item-req';
export type { PictureInfo } from './models/picture-info';
export type { PreOrderSetting } from './models/pre-order-setting';
export type { QuotationItemModel } from './models/quotation-item-model';
export type { RegularChangeDetail } from './models/regular-change-detail';
export type { RegularDetail } from './models/regular-detail';
export type { RegularNoticeFileList } from './models/regular-notice-file-list';
export type { RegularRemark } from './models/regular-remark';
export type { RegularRemarkArgs } from './models/regular-remark-args';
export type { ReviewHouseHold } from './models/review-house-hold';
export type { SaveBuildCaseArgs } from './models/save-build-case-args';
export type { SaveBuildCaseMailRequest } from './models/save-build-case-mail-request';
export type { SaveDataQuotation } from './models/save-data-quotation';
export type { SaveDataQuotationResponseBase } from './models/save-data-quotation-response-base';
export type { SaveDataRequirement } from './models/save-data-requirement';
export type { SaveHouseChangeDateReq } from './models/save-house-change-date-req';
export type { SaveListFormItemReq } from './models/save-list-form-item-req';
export type { SaveMaterialArgs } from './models/save-material-args';
export type { SavePreOrderSetting } from './models/save-pre-order-setting';
export type { SaveRegularChangeDetailRequest } from './models/save-regular-change-detail-request';
export type { SaveSpaceRequest } from './models/save-space-request';
export type { SaveTemplateArgs } from './models/save-template-args';
export type { SaveTemplateDetailArgs } from './models/save-template-detail-args';
export type { SaveTemplayeDataReq } from './models/save-templaye-data-req';
export type { SignQuotation } from './models/sign-quotation';
export type { SpecialChangeAvailableArgs } from './models/special-change-available-args';
export type { SpecialChangeAvailableRes } from './models/special-change-available-res';
export type { SpecialChangeAvailableResListResponseBase } from './models/special-change-available-res-list-response-base';
export type { SpecialChangeFile } from './models/special-change-file';
export type { SpecialChangeFileGroup } from './models/special-change-file-group';
export type { SpecialChangeFileGroupListResponseBase } from './models/special-change-file-group-list-response-base';
export type { SpecialChangeFileRespone } from './models/special-change-file-respone';
export type { SpecialChangeRes } from './models/special-change-res';
export type { SpecialChangeResListResponseBase } from './models/special-change-res-list-response-base';
export type { SpecialChangeResResponseBase } from './models/special-change-res-response-base';
export type { SpecialNoticeFileList } from './models/special-notice-file-list';
export type { StringHouseDropDownItemListDictionaryResponseBase } from './models/string-house-drop-down-item-list-dictionary-response-base';
export type { StringListResponseBase } from './models/string-list-response-base';
export type { StringResponseBase } from './models/string-response-base';
export type { TblExamineLog } from './models/tbl-examine-log';
export type { TblFinalDocument } from './models/tbl-final-document';
export type { TblFinalDocumentListResponseBase } from './models/tbl-final-document-list-response-base';
export type { TblFormItemHousehold } from './models/tbl-form-item-household';
export type { TblHouse } from './models/tbl-house';
export type { TblHousePreorderDetail } from './models/tbl-house-preorder-detail';
export type { TblHousePreorderDetailListResponseBase } from './models/tbl-house-preorder-detail-list-response-base';
export type { TblHouseResponseBase } from './models/tbl-house-response-base';
export type { TblPicture } from './models/tbl-picture';
export type { TblQuotationItem } from './models/tbl-quotation-item';
export type { TblRegularNoticeFile } from './models/tbl-regular-notice-file';
export type { TblRegularNoticeFileHouse } from './models/tbl-regular-notice-file-house';
export type { TblRegularNoticeFileResponseBase } from './models/tbl-regular-notice-file-response-base';
export type { TblReview } from './models/tbl-review';
export type { TblSpecialNoticeFile } from './models/tbl-special-notice-file';
export type { TblSpecialNoticeFileResponseBase } from './models/tbl-special-notice-file-response-base';
export type { TemplateDetailItem } from './models/template-detail-item';
export type { TemplateDetailItemListResponseBase } from './models/template-detail-item-list-response-base';
export type { TemplateGetListArgs } from './models/template-get-list-args';
export type { TemplateGetListResponse } from './models/template-get-list-response';
export type { TemplateGetListResponseListResponseBase } from './models/template-get-list-response-list-response-base';
export type { TemplateGetListResponseResponseBase } from './models/template-get-list-response-response-base';
export type { UnlockFormItem } from './models/unlock-form-item';
export type { UpdateApproveWaiting } from './models/update-approve-waiting';
export type { UpdateHouseRequirementArgs } from './models/update-house-requirement-args';
export type { UpdateHouseReviewArgs } from './models/update-house-review-args';
export type { UpdateSignArgs } from './models/update-sign-args';
export type { UploadFileResponse } from './models/upload-file-response';
export type { UploadFileResponseResponseBase } from './models/upload-file-response-response-base';
export type { UploadRegularPic } from './models/upload-regular-pic';
export type { UploadSpecialChangeFile } from './models/upload-special-change-file';
export type { UserBuildCase } from './models/user-build-case';
export type { UserGetDataArgs } from './models/user-get-data-args';
export type { UserGetDataResponse } from './models/user-get-data-response';
export type { UserGetDataResponseResponseBase } from './models/user-get-data-response-response-base';
export type { UserGetListArgs } from './models/user-get-list-args';
export type { UserGetListResponse } from './models/user-get-list-response';
export type { UserGetListResponseListResponseBase } from './models/user-get-list-response-list-response-base';
export type { UserGetUserLogArgs } from './models/user-get-user-log-args';
export type { UserGetUserLogResponse } from './models/user-get-user-log-response';
export type { UserGetUserLogResponseListResponseBase } from './models/user-get-user-log-response-list-response-base';
export type { UserGroupGetDataArgs } from './models/user-group-get-data-args';
export type { UserGroupGetDataAuthority } from './models/user-group-get-data-authority';
export type { UserGroupGetDataFunctionLv1 } from './models/user-group-get-data-function-lv-1';
export type { UserGroupGetDataFunctionLv2 } from './models/user-group-get-data-function-lv-2';
export type { UserGroupGetDataResponse } from './models/user-group-get-data-response';
export type { UserGroupGetDataResponseResponseBase } from './models/user-group-get-data-response-response-base';
export type { UserGroupGetListArgs } from './models/user-group-get-list-args';
export type { UserGroupGetListResponse } from './models/user-group-get-list-response';
export type { UserGroupGetListResponseListResponseBase } from './models/user-group-get-list-response-list-response-base';
export type { UserGroupRemoveDataArgs } from './models/user-group-remove-data-args';
export type { UserGroupRemoveDataResponse } from './models/user-group-remove-data-response';
export type { UserGroupRemoveDataResponseResponseBase } from './models/user-group-remove-data-response-response-base';
export type { UserGroupSaveDataArgs } from './models/user-group-save-data-args';
export type { UserGroupSaveDataResponse } from './models/user-group-save-data-response';
export type { UserGroupSaveDataResponseResponseBase } from './models/user-group-save-data-response-response-base';
export type { UserLoginRequest } from './models/user-login-request';
export type { UserRemoveDataArgs } from './models/user-remove-data-args';
export type { UserRemoveDataResponse } from './models/user-remove-data-response';
export type { UserRemoveDataResponseResponseBase } from './models/user-remove-data-response-response-base';
export type { UserSaveDataArgs } from './models/user-save-data-args';
export type { UserSaveDataResponse } from './models/user-save-data-response';
export type { UserSaveDataResponseResponseBase } from './models/user-save-data-response-response-base';
