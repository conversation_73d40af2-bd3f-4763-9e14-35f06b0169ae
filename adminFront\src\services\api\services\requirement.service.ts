/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { apiRequirementBatchSaveDataPost$Json } from '../fn/requirement/api-requirement-batch-save-data-post-json';
import { ApiRequirementBatchSaveDataPost$Json$Params } from '../fn/requirement/api-requirement-batch-save-data-post-json';
import { apiRequirementBatchSaveDataPost$Plain } from '../fn/requirement/api-requirement-batch-save-data-post-plain';
import { ApiRequirementBatchSaveDataPost$Plain$Params } from '../fn/requirement/api-requirement-batch-save-data-post-plain';
import { apiRequirementDeleteDataPost$Json } from '../fn/requirement/api-requirement-delete-data-post-json';
import { ApiRequirementDeleteDataPost$Json$Params } from '../fn/requirement/api-requirement-delete-data-post-json';
import { apiRequirementDeleteDataPost$Plain } from '../fn/requirement/api-requirement-delete-data-post-plain';
import { ApiRequirementDeleteDataPost$Plain$Params } from '../fn/requirement/api-requirement-delete-data-post-plain';
import { apiRequirementGetDataPost$Json } from '../fn/requirement/api-requirement-get-data-post-json';
import { ApiRequirementGetDataPost$Json$Params } from '../fn/requirement/api-requirement-get-data-post-json';
import { apiRequirementGetDataPost$Plain } from '../fn/requirement/api-requirement-get-data-post-plain';
import { ApiRequirementGetDataPost$Plain$Params } from '../fn/requirement/api-requirement-get-data-post-plain';
import { apiRequirementGetListPost$Json } from '../fn/requirement/api-requirement-get-list-post-json';
import { ApiRequirementGetListPost$Json$Params } from '../fn/requirement/api-requirement-get-list-post-json';
import { apiRequirementGetListPost$Plain } from '../fn/requirement/api-requirement-get-list-post-plain';
import { ApiRequirementGetListPost$Plain$Params } from '../fn/requirement/api-requirement-get-list-post-plain';
import { apiRequirementSaveDataPost$Json } from '../fn/requirement/api-requirement-save-data-post-json';
import { ApiRequirementSaveDataPost$Json$Params } from '../fn/requirement/api-requirement-save-data-post-json';
import { apiRequirementSaveDataPost$Plain } from '../fn/requirement/api-requirement-save-data-post-plain';
import { ApiRequirementSaveDataPost$Plain$Params } from '../fn/requirement/api-requirement-save-data-post-plain';
import { apiRequirementSaveTemplayeDataPost$Json } from '../fn/requirement/api-requirement-save-templaye-data-post-json';
import { ApiRequirementSaveTemplayeDataPost$Json$Params } from '../fn/requirement/api-requirement-save-templaye-data-post-json';
import { apiRequirementSaveTemplayeDataPost$Plain } from '../fn/requirement/api-requirement-save-templaye-data-post-plain';
import { ApiRequirementSaveTemplayeDataPost$Plain$Params } from '../fn/requirement/api-requirement-save-templaye-data-post-plain';
import { GetRequirementListResponseBase } from '../models/get-requirement-list-response-base';
import { GetRequirementResponseBase } from '../models/get-requirement-response-base';
import { StringResponseBase } from '../models/string-response-base';

@Injectable({ providedIn: 'root' })
export class RequirementService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `apiRequirementGetListPost()` */
  static readonly ApiRequirementGetListPostPath = '/api/Requirement/GetList';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementGetListPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementGetListPost$Plain$Response(params?: ApiRequirementGetListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRequirementListResponseBase>> {
    return apiRequirementGetListPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementGetListPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementGetListPost$Plain(params?: ApiRequirementGetListPost$Plain$Params, context?: HttpContext): Observable<GetRequirementListResponseBase> {
    return this.apiRequirementGetListPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetRequirementListResponseBase>): GetRequirementListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementGetListPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementGetListPost$Json$Response(params?: ApiRequirementGetListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRequirementListResponseBase>> {
    return apiRequirementGetListPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementGetListPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementGetListPost$Json(params?: ApiRequirementGetListPost$Json$Params, context?: HttpContext): Observable<GetRequirementListResponseBase> {
    return this.apiRequirementGetListPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetRequirementListResponseBase>): GetRequirementListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiRequirementGetDataPost()` */
  static readonly ApiRequirementGetDataPostPath = '/api/Requirement/GetData';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementGetDataPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementGetDataPost$Plain$Response(params?: ApiRequirementGetDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRequirementResponseBase>> {
    return apiRequirementGetDataPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementGetDataPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementGetDataPost$Plain(params?: ApiRequirementGetDataPost$Plain$Params, context?: HttpContext): Observable<GetRequirementResponseBase> {
    return this.apiRequirementGetDataPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetRequirementResponseBase>): GetRequirementResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementGetDataPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementGetDataPost$Json$Response(params?: ApiRequirementGetDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRequirementResponseBase>> {
    return apiRequirementGetDataPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementGetDataPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementGetDataPost$Json(params?: ApiRequirementGetDataPost$Json$Params, context?: HttpContext): Observable<GetRequirementResponseBase> {
    return this.apiRequirementGetDataPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetRequirementResponseBase>): GetRequirementResponseBase => r.body)
    );
  }

  /** Path part for operation `apiRequirementSaveDataPost()` */
  static readonly ApiRequirementSaveDataPostPath = '/api/Requirement/SaveData';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementSaveDataPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementSaveDataPost$Plain$Response(params?: ApiRequirementSaveDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiRequirementSaveDataPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementSaveDataPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementSaveDataPost$Plain(params?: ApiRequirementSaveDataPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiRequirementSaveDataPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementSaveDataPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementSaveDataPost$Json$Response(params?: ApiRequirementSaveDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiRequirementSaveDataPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementSaveDataPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementSaveDataPost$Json(params?: ApiRequirementSaveDataPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiRequirementSaveDataPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /** Path part for operation `apiRequirementBatchSaveDataPost()` */
  static readonly ApiRequirementBatchSaveDataPostPath = '/api/Requirement/BatchSaveData';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementBatchSaveDataPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementBatchSaveDataPost$Plain$Response(params?: ApiRequirementBatchSaveDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiRequirementBatchSaveDataPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementBatchSaveDataPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementBatchSaveDataPost$Plain(params?: ApiRequirementBatchSaveDataPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiRequirementBatchSaveDataPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementBatchSaveDataPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementBatchSaveDataPost$Json$Response(params?: ApiRequirementBatchSaveDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiRequirementBatchSaveDataPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementBatchSaveDataPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementBatchSaveDataPost$Json(params?: ApiRequirementBatchSaveDataPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiRequirementBatchSaveDataPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /** Path part for operation `apiRequirementDeleteDataPost()` */
  static readonly ApiRequirementDeleteDataPostPath = '/api/Requirement/DeleteData';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementDeleteDataPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementDeleteDataPost$Plain$Response(params?: ApiRequirementDeleteDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiRequirementDeleteDataPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementDeleteDataPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementDeleteDataPost$Plain(params?: ApiRequirementDeleteDataPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiRequirementDeleteDataPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementDeleteDataPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementDeleteDataPost$Json$Response(params?: ApiRequirementDeleteDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiRequirementDeleteDataPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementDeleteDataPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementDeleteDataPost$Json(params?: ApiRequirementDeleteDataPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiRequirementDeleteDataPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /** Path part for operation `apiRequirementSaveTemplayeDataPost()` */
  static readonly ApiRequirementSaveTemplayeDataPostPath = '/api/Requirement/SaveTemplayeData';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementSaveTemplayeDataPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementSaveTemplayeDataPost$Plain$Response(params?: ApiRequirementSaveTemplayeDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiRequirementSaveTemplayeDataPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementSaveTemplayeDataPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementSaveTemplayeDataPost$Plain(params?: ApiRequirementSaveTemplayeDataPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiRequirementSaveTemplayeDataPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiRequirementSaveTemplayeDataPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementSaveTemplayeDataPost$Json$Response(params?: ApiRequirementSaveTemplayeDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiRequirementSaveTemplayeDataPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiRequirementSaveTemplayeDataPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiRequirementSaveTemplayeDataPost$Json(params?: ApiRequirementSaveTemplayeDataPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiRequirementSaveTemplayeDataPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

}
